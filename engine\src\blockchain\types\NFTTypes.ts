/**
 * NFT相关类型定义
 */

export interface NFTMetadata {
  name: string;
  description: string;
  image: string;
  external_url?: string;
  attributes: NFTAttribute[];
  animation_url?: string;
  background_color?: string;
  youtube_url?: string;
  // DL引擎特定属性
  dl_engine_data: DLEngineNFTData;
}

export interface NFTAttribute {
  trait_type: string;
  value: string | number;
  display_type?: 'boost_number' | 'boost_percentage' | 'number' | 'date';
  max_value?: number;
}

export interface DLEngineNFTData {
  scene_id?: string;
  asset_type: AssetType;
  engine_version: string;
  creation_timestamp: number;
  creator_address: string;
  license_type: LicenseType;
  educational_metadata?: EducationalMetadata;
  technical_metadata?: TechnicalMetadata;
  interaction_metadata?: InteractionMetadata;
}

export interface EducationalMetadata {
  subject: string;
  grade_level: string;
  learning_objectives: string[];
  difficulty_level: number; // 1-10
  estimated_duration: number; // 分钟
  prerequisites: string[];
  tags: string[];
  language: string;
  accessibility_features: string[];
}

export interface TechnicalMetadata {
  file_format: string;
  file_size: number;
  resolution?: string;
  frame_rate?: number;
  duration?: number;
  compression: string;
  quality_level: number; // 1-10
  polygon_count?: number;
  texture_count?: number;
  animation_count?: number;
}

export interface InteractionMetadata {
  interaction_types: InteractionType[];
  input_methods: InputMethod[];
  supported_devices: DeviceType[];
  vr_compatible: boolean;
  ar_compatible: boolean;
  multiplayer_support: boolean;
  max_participants?: number;
}

export interface NFTToken {
  tokenId: string;
  contractAddress: string;
  chainId: number;
  owner: string;
  creator: string;
  metadata: NFTMetadata;
  tokenURI: string;
  mintedAt: Date;
  lastTransferAt?: Date;
  transferCount: number;
  isForSale: boolean;
  price?: string;
  currency?: string;
  royaltyRecipient?: string;
  royaltyPercentage?: number;
}

export interface RoyaltyInfo {
  recipient: string;
  percentage: number; // 0-10000 (basis points)
}

export interface NFTCollection {
  contractAddress: string;
  chainId: number;
  name: string;
  symbol: string;
  description: string;
  image: string;
  external_link?: string;
  creator: string;
  totalSupply: number;
  floorPrice?: string;
  volume24h?: string;
  owners: number;
  verified: boolean;
  category: NFTCategory;
}

export interface NFTMarketplaceListing {
  listingId: string;
  tokenId: string;
  contractAddress: string;
  seller: string;
  price: string;
  currency: string;
  startTime: Date;
  endTime?: Date;
  status: ListingStatus;
  bids?: NFTBid[];
  reservePrice?: string;
  buyNowPrice?: string;
}

export interface NFTBid {
  bidId: string;
  bidder: string;
  amount: string;
  currency: string;
  timestamp: Date;
  expiresAt?: Date;
  status: BidStatus;
}

export interface NFTTransfer {
  transactionHash: string;
  blockNumber: number;
  timestamp: Date;
  from: string;
  to: string;
  tokenId: string;
  contractAddress: string;
  value?: string; // 如果是销售
}

export interface NFTRenderOptions {
  quality?: 'low' | 'medium' | 'high' | 'ultra';
  enableAnimations?: boolean;
  enableInteractions?: boolean;
  enablePhysics?: boolean;
  enableAudio?: boolean;
  maxTextureSize?: number;
  lodLevel?: number;
  renderDistance?: number;
  position?: { x: number; y: number; z: number };
  rotation?: { x: number; y: number; z: number };
  scale?: { x: number; y: number; z: number };
  visible?: boolean;
  autoPlay?: boolean;
  loop?: boolean;
  volume?: number;
}

export interface NFTDisplayConfig {
  showMetadata: boolean;
  showOwnership: boolean;
  showPricing: boolean;
  showHistory: boolean;
  enableFullscreen: boolean;
  enableSharing: boolean;
  enableDownload: boolean;
  watermarkEnabled: boolean;
}

export enum AssetType {
  MODEL_3D = 'model_3d',
  MODEL = 'model',
  TEXTURE = 'texture',
  AUDIO = 'audio',
  SCENE = 'scene',
  ANIMATION = 'animation',
  MATERIAL = 'material',
  SCRIPT = 'script',
  ENVIRONMENT = 'environment',
  CHARACTER = 'character',
  PROP = 'prop'
}

export enum LicenseType {
  CC0 = 'CC0',
  CC_BY = 'CC-BY',
  CC_BY_SA = 'CC-BY-SA',
  CC_BY_NC = 'CC-BY-NC',
  CC_BY_NC_SA = 'CC-BY-NC-SA',
  COMMERCIAL = 'Commercial',
  EDUCATIONAL = 'Educational',
  CUSTOM = 'Custom'
}

export enum InteractionType {
  CLICK = 'click',
  HOVER = 'hover',
  DRAG = 'drag',
  ROTATE = 'rotate',
  SCALE = 'scale',
  ANIMATE = 'animate',
  PHYSICS = 'physics',
  AUDIO_TRIGGER = 'audio_trigger',
  SCRIPT_EXECUTION = 'script_execution'
}

export enum InputMethod {
  MOUSE = 'mouse',
  KEYBOARD = 'keyboard',
  TOUCH = 'touch',
  GAMEPAD = 'gamepad',
  VR_CONTROLLER = 'vr_controller',
  HAND_TRACKING = 'hand_tracking',
  EYE_TRACKING = 'eye_tracking',
  VOICE = 'voice'
}

export enum DeviceType {
  DESKTOP = 'desktop',
  MOBILE = 'mobile',
  TABLET = 'tablet',
  VR_HEADSET = 'vr_headset',
  AR_DEVICE = 'ar_device',
  SMART_TV = 'smart_tv'
}

export enum NFTCategory {
  EDUCATIONAL = 'educational',
  ENTERTAINMENT = 'entertainment',
  ART = 'art',
  UTILITY = 'utility',
  COLLECTIBLE = 'collectible',
  GAMING = 'gaming',
  VIRTUAL_WORLD = 'virtual_world',
  SCIENTIFIC = 'scientific'
}

export enum ListingStatus {
  ACTIVE = 'active',
  SOLD = 'sold',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired'
}

export enum BidStatus {
  ACTIVE = 'active',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
  WITHDRAWN = 'withdrawn'
}

export enum NFTStandard {
  ERC721 = 'ERC721',
  ERC1155 = 'ERC1155'
}

export enum MintingStatusEnum {
  PREPARING = 'preparing',
  UPLOADING_METADATA = 'uploading_metadata',
  UPLOADING_ASSETS = 'uploading_assets',
  MINTING = 'minting',
  CONFIRMING = 'confirming',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// 铸造状态信息
export interface MintingStatus {
  assetId: string;
  status: MintingStatusEnum;
  progress: number; // 0-100
  startTime: Date;
  lastUpdate?: Date;
  metadata: NFTMetadata;
  error?: string;
  transactionHash?: string;
}

// NFT操作结果
export interface NFTOperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  transactionHash?: string;
  gasUsed?: string;
  gasCost?: string;
}

// NFT搜索过滤器
export interface NFTSearchFilter {
  category?: NFTCategory;
  assetType?: AssetType;
  priceRange?: {
    min: string;
    max: string;
    currency: string;
  };
  creator?: string;
  owner?: string;
  traits?: Record<string, string | number>;
  educationalLevel?: string;
  subject?: string;
  language?: string;
  sortBy?: 'price' | 'created' | 'popularity' | 'name';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}
