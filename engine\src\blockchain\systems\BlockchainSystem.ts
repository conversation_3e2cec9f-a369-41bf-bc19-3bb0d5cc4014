/**
 * 区块链系统 - 将区块链功能集成到DL引擎的ECS架构中
 */

import { System } from '../../core/System';
import { Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { BlockchainManager } from '../core/BlockchainManager';
import { NFTManager } from '../nft/NFTManager';
import { BlockchainComponent } from '../components/BlockchainComponent';
import { NFTComponent } from '../components/NFTComponent';
import {
  BlockchainConfig,
  BlockchainEventType,
  BlockchainState
} from '../types/BlockchainTypes';
import { NFTToken } from '../types/NFTTypes';

// 钱包组件类 - 继承自Component基类
class WalletComponent extends Component {
  public address: string;

  constructor(address: string) {
    super('WalletComponent');
    this.address = address;
  }

  update(deltaTime: number): void {
    super.update(deltaTime);
    // 钱包组件更新逻辑
  }

  setAddress(address: string): void {
    this.address = address;
  }

  protected onAttach(): void {
    super.onAttach();
    // 组件附加到实体时的逻辑
  }

  public dispose(): void {
    super.dispose();
    // 组件销毁逻辑
  }
}

export class BlockchainSystem extends System {
  private blockchainManager: BlockchainManager;
  private nftManager: NFTManager;
  private isInitialized: boolean = false;
  private updateInterval: number = 5000; // 5秒更新一次
  private lastUpdateTime: number = 0;

  constructor(config: BlockchainConfig, priority: number = 0) {
    super(priority);

    this.blockchainManager = new BlockchainManager(config);
    this.nftManager = new NFTManager(this.blockchainManager);

    this.setupEventListeners();
  }

  /**
   * 系统初始化
   */
  async initialize(): Promise<void> {
    try {
      console.log('初始化区块链系统...');
      
      // 初始化区块链管理器
      await this.blockchainManager.initialize();
      
      // 初始化NFT管理器
      await this.nftManager.initialize();
      
      this.isInitialized = true;
      console.log('区块链系统初始化完成');
      
    } catch (error) {
      console.error('区块链系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 系统更新
   */
  update(deltaTime: number): void {
    if (!this.isInitialized) {
      return;
    }

    const currentTime = Date.now();
    if (currentTime - this.lastUpdateTime < this.updateInterval) {
      return;
    }

    this.lastUpdateTime = currentTime;

    // 更新所有区块链组件
    this.updateBlockchainComponents(deltaTime);

    // 更新所有NFT组件
    this.updateNFTComponents(deltaTime);

    // 更新所有钱包组件
    this.updateWalletComponents(deltaTime);
  }

  /**
   * 连接钱包
   */
  async connectWallet(walletType?: string): Promise<boolean> {
    try {
      const result = await this.blockchainManager.connectWallet(walletType);
      
      if (result.success) {
        // 创建或更新钱包组件
        this.createOrUpdateWalletComponent(result.data!);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('连接钱包失败:', error);
      return false;
    }
  }

  /**
   * 断开钱包连接
   */
  async disconnectWallet(): Promise<void> {
    try {
      await this.blockchainManager.disconnectWallet();
      
      // 移除钱包组件
      this.removeWalletComponents();
      
    } catch (error) {
      console.error('断开钱包连接失败:', error);
    }
  }

  /**
   * 为实体添加区块链功能
   */
  addBlockchainToEntity(entity: Entity, config?: any): BlockchainComponent {
    const component = new BlockchainComponent(config);
    entity.addComponent(component as any);
    return component;
  }

  /**
   * 为实体添加NFT功能
   */
  addNFTToEntity(entity: Entity, nft: NFTToken): NFTComponent {
    const component = new NFTComponent(nft);
    entity.addComponent(component as any);
    return component;
  }

  /**
   * 铸造实体为NFT
   */
  async mintEntityAsNFT(
    entity: Entity,
    metadata: any,
    royaltyRecipient?: string,
    royaltyPercentage?: number
  ): Promise<boolean> {
    try {
      // 获取实体的资产ID（假设实体有资产组件）
      const assetId = this.getEntityAssetId(entity);
      if (!assetId) {
        throw new Error('实体没有关联的资产ID');
      }

      // 铸造NFT
      const result = await this.nftManager.mintNFT(
        assetId,
        metadata,
        royaltyRecipient,
        royaltyPercentage
      );

      if (result.success && result.data) {
        // 为实体添加NFT组件
        this.addNFTToEntity(entity, result.data);
        return true;
      }

      return false;
    } catch (error) {
      console.error('铸造实体NFT失败:', error);
      return false;
    }
  }

  /**
   * 展示NFT实体
   */
  async displayNFTEntity(
    tokenId: string,
    contractAddress: string,
    position?: { x: number; y: number; z: number }
  ): Promise<Entity | null> {
    try {
      // 获取NFT信息
      const nft = await this.nftManager.getNFTInfo(tokenId, contractAddress);
      if (!nft) {
        throw new Error('NFT不存在');
      }

      // 创建新实体
      const entity = this.world!.createEntity();

      // 添加NFT组件
      this.addNFTToEntity(entity, nft);

      // 添加变换组件（如果指定了位置）
      if (position) {
        const transform = entity.getComponent('Transform');
        if (transform && (transform as any).position) {
          (transform as any).position.set(position.x, position.y, position.z);
        }
      }

      // 展示NFT
      const displayResult = await this.nftManager.displayNFT(tokenId, contractAddress);
      if (displayResult.success) {
        console.log('NFT实体创建成功:', entity.id);
        return entity;
      }

      // 如果展示失败，移除实体
      this.world!.removeEntity(entity);
      return null;
      
    } catch (error) {
      console.error('展示NFT实体失败:', error);
      return null;
    }
  }

  /**
   * 获取区块链状态
   */
  getBlockchainState(): BlockchainState {
    return this.blockchainManager.getState();
  }

  /**
   * 获取用户NFT列表
   */
  getUserNFTs(): NFTToken[] {
    return this.nftManager.getUserNFTs();
  }

  /**
   * 获取区块链管理器
   */
  getBlockchainManager(): BlockchainManager {
    return this.blockchainManager;
  }

  /**
   * 获取NFT管理器
   */
  getNFTManager(): NFTManager {
    return this.nftManager;
  }

  /**
   * 更新区块链组件
   */
  private updateBlockchainComponents(deltaTime: number): void {
    if (!this.world) return;

    const entities = this.getEntitiesWithComponent('BlockchainComponent');

    for (const entity of entities) {
      const component = entity.getComponent('BlockchainComponent') as unknown as BlockchainComponent;
      if (component && (component as any).update) {
        (component as any).update(deltaTime);
      }
    }
  }

  /**
   * 更新NFT组件
   */
  private updateNFTComponents(deltaTime: number): void {
    if (!this.world) return;

    const entities = this.getEntitiesWithComponent('NFTComponent');

    for (const entity of entities) {
      const component = entity.getComponent('NFTComponent') as unknown as NFTComponent;
      if (component && (component as any).update) {
        (component as any).update(deltaTime);
      }
    }
  }

  /**
   * 更新钱包组件
   */
  private updateWalletComponents(deltaTime: number): void {
    if (!this.world) return;

    const entities = this.getEntitiesWithComponent('WalletComponent');

    for (const entity of entities) {
      const component = entity.getComponent('WalletComponent') as unknown as WalletComponent;
      if (component && component.update) {
        component.update(deltaTime);
      }
    }
  }

  /**
   * 获取具有指定组件的实体
   */
  private getEntitiesWithComponent(componentType: string): Entity[] {
    if (!this.world) return [];

    const entities = this.world.getAllEntities();
    return entities.filter(entity => entity.getComponent(componentType) !== null);
  }

  /**
   * 创建或更新钱包组件
   */
  private createOrUpdateWalletComponent(address: string): void {
    if (!this.world) return;

    // 查找现有的钱包实体
    let walletEntity = this.findWalletEntity();

    if (!walletEntity) {
      // 创建新的钱包实体
      walletEntity = this.world.createEntity();
    }

    // 添加或更新钱包组件
    let walletComponent = walletEntity.getComponent('WalletComponent') as unknown as WalletComponent;
    if (!walletComponent) {
      walletComponent = new WalletComponent(address);
      walletEntity.addComponent(walletComponent as any);
    } else {
      walletComponent.setAddress(address);
    }
  }

  /**
   * 移除钱包组件
   */
  private removeWalletComponents(): void {
    if (!this.world) return;

    const walletEntity = this.findWalletEntity();
    if (walletEntity) {
      this.world.removeEntity(walletEntity);
    }
  }

  /**
   * 查找钱包实体
   */
  private findWalletEntity(): Entity | null {
    const entities = this.getEntitiesWithComponent('WalletComponent');
    return entities.length > 0 ? entities[0] : null;
  }

  /**
   * 获取实体的资产ID
   */
  private getEntityAssetId(entity: Entity): string | null {
    // 这里应该根据实际的资产组件实现来获取资产ID
    // 假设实体有一个AssetComponent
    const assetComponent = entity.getComponent('AssetComponent');
    return assetComponent ? (assetComponent as any).id : null;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.blockchainManager.on(BlockchainEventType.WALLET_CONNECTED, (data) => {
      console.log('钱包已连接:', data);
      this.emit('walletConnected', data);
    });

    this.blockchainManager.on(BlockchainEventType.WALLET_DISCONNECTED, () => {
      console.log('钱包已断开');
      this.emit('walletDisconnected');
    });

    this.blockchainManager.on(BlockchainEventType.NETWORK_CHANGED, (network) => {
      console.log('网络已切换:', network);
      this.emit('networkChanged', network);
    });

    this.nftManager.on('nftMinted', (nft) => {
      console.log('NFT已铸造:', nft);
      this.emit('nftMinted', nft);
    });

    this.nftManager.on('nftDisplayed', (data) => {
      console.log('NFT已显示:', data);
      this.emit('nftDisplayed', data);
    });
  }

  /**
   * 系统销毁
   */
  async destroy(): Promise<void> {
    try {
      await this.nftManager.destroy();
      await this.blockchainManager.destroy();
      this.removeAllListeners();
      this.isInitialized = false;
      
      console.log('区块链系统已销毁');
    } catch (error) {
      console.error('销毁区块链系统失败:', error);
    }
  }
}
