/**
 * NFT渲染器 - 负责在3D场景中渲染和显示NFT
 */

import { EventEmitter } from '../../utils/EventEmitter';
import { NFTManager } from './NFTManager';
import { 
  NFTToken, 
  NFTRenderOptions, 
  NFTOperationResult,
  AssetType 
} from '../types/NFTTypes';

export class NFTRenderer extends EventEmitter {
  private nftManager: NFTManager;
  private scene: any; // 3D场景引用
  private renderedObjects: Map<string, any> = new Map();
  private isInitialized: boolean = false;

  constructor(nftManager: NFTManager) {
    super();
    this.nftManager = nftManager;
  }

  /**
   * 初始化NFT渲染器
   */
  async initialize(): Promise<void> {
    try {
      console.log('初始化NFT渲染器...');
      
      // 获取3D场景引用
      // 这里应该从DL引擎获取场景实例
      
      this.isInitialized = true;
      console.log('NFT渲染器初始化完成');
    } catch (error) {
      console.error('NFT渲染器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 渲染NFT到场景中
   */
  async renderNFT(
    nft: NFTToken, 
    options?: NFTRenderOptions
  ): Promise<NFTOperationResult<any>> {
    try {
      console.log('渲染NFT:', nft.tokenId);
      
      if (!this.isInitialized) {
        throw new Error('NFT渲染器未初始化');
      }

      // 根据NFT类型选择渲染方式
      let renderObject: any;
      
      switch (nft.metadata.dl_engine_data?.asset_type) {
        case AssetType.MODEL_3D:
          renderObject = await this.render3DModel(nft, options);
          break;
        case AssetType.TEXTURE:
          renderObject = await this.renderTexture(nft, options);
          break;
        case AssetType.ANIMATION:
          renderObject = await this.renderAnimation(nft, options);
          break;
        case AssetType.AUDIO:
          renderObject = await this.renderAudio(nft, options);
          break;
        case AssetType.SCENE:
          renderObject = await this.renderScene(nft, options);
          break;
        default:
          renderObject = await this.renderDefault(nft, options);
          break;
      }

      if (renderObject) {
        const nftKey = `${nft.contractAddress}-${nft.tokenId}`;
        this.renderedObjects.set(nftKey, renderObject);
        
        this.emit('nftRendered', {
          nft,
          renderObject,
          options
        });
      }

      return {
        success: true,
        data: renderObject
      };
    } catch (error) {
      console.error('渲染NFT失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '渲染NFT失败'
      };
    }
  }

  /**
   * 从场景中移除NFT
   */
  async removeNFT(renderObject: any): Promise<void> {
    try {
      if (!renderObject) return;
      
      // 从场景中移除对象
      if (this.scene && renderObject.parent) {
        renderObject.parent.remove(renderObject);
      }
      
      // 清理资源
      if (renderObject.geometry) {
        renderObject.geometry.dispose();
      }
      if (renderObject.material) {
        if (Array.isArray(renderObject.material)) {
          renderObject.material.forEach((mat: any) => mat.dispose());
        } else {
          renderObject.material.dispose();
        }
      }
      
      this.emit('nftRemoved', renderObject);
    } catch (error) {
      console.error('移除NFT失败:', error);
    }
  }

  /**
   * 更新NFT渲染
   */
  async updateNFTRender(
    nft: NFTToken, 
    options: NFTRenderOptions
  ): Promise<NFTOperationResult<any>> {
    try {
      const nftKey = `${nft.contractAddress}-${nft.tokenId}`;
      const existingObject = this.renderedObjects.get(nftKey);
      
      if (existingObject) {
        // 移除现有对象
        await this.removeNFT(existingObject);
        this.renderedObjects.delete(nftKey);
      }
      
      // 重新渲染
      return await this.renderNFT(nft, options);
    } catch (error) {
      console.error('更新NFT渲染失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新NFT渲染失败'
      };
    }
  }

  /**
   * 渲染3D模型NFT
   */
  private async render3DModel(nft: NFTToken, options?: NFTRenderOptions): Promise<any> {
    try {
      // 这里应该实现3D模型加载和渲染逻辑
      console.log('渲染3D模型NFT:', nft.metadata.name);
      
      // 模拟创建3D对象
      const mockObject = {
        type: '3DModel',
        nftId: nft.tokenId,
        position: options?.position || { x: 0, y: 0, z: 0 },
        rotation: options?.rotation || { x: 0, y: 0, z: 0 },
        scale: options?.scale || { x: 1, y: 1, z: 1 },
        visible: options?.visible !== false
      };
      
      return mockObject;
    } catch (error) {
      console.error('渲染3D模型失败:', error);
      throw error;
    }
  }

  /**
   * 渲染纹理NFT
   */
  private async renderTexture(nft: NFTToken, options?: NFTRenderOptions): Promise<any> {
    try {
      console.log('渲染纹理NFT:', nft.metadata.name);
      
      const mockObject = {
        type: 'Texture',
        nftId: nft.tokenId,
        imageUrl: nft.metadata.image,
        position: options?.position || { x: 0, y: 0, z: 0 },
        scale: options?.scale || { x: 1, y: 1, z: 1 },
        visible: options?.visible !== false
      };
      
      return mockObject;
    } catch (error) {
      console.error('渲染纹理失败:', error);
      throw error;
    }
  }

  /**
   * 渲染动画NFT
   */
  private async renderAnimation(nft: NFTToken, options?: NFTRenderOptions): Promise<any> {
    try {
      console.log('渲染动画NFT:', nft.metadata.name);
      
      const mockObject = {
        type: 'Animation',
        nftId: nft.tokenId,
        animationUrl: nft.metadata.animation_url,
        position: options?.position || { x: 0, y: 0, z: 0 },
        autoPlay: options?.autoPlay !== false,
        loop: options?.loop !== false,
        visible: options?.visible !== false
      };
      
      return mockObject;
    } catch (error) {
      console.error('渲染动画失败:', error);
      throw error;
    }
  }

  /**
   * 渲染音频NFT
   */
  private async renderAudio(nft: NFTToken, options?: NFTRenderOptions): Promise<any> {
    try {
      console.log('渲染音频NFT:', nft.metadata.name);
      
      const mockObject = {
        type: 'Audio',
        nftId: nft.tokenId,
        audioUrl: nft.metadata.animation_url,
        position: options?.position || { x: 0, y: 0, z: 0 },
        autoPlay: options?.autoPlay === true,
        loop: options?.loop === true,
        volume: options?.volume || 1.0,
        visible: options?.visible !== false
      };
      
      return mockObject;
    } catch (error) {
      console.error('渲染音频失败:', error);
      throw error;
    }
  }

  /**
   * 渲染场景NFT
   */
  private async renderScene(nft: NFTToken, options?: NFTRenderOptions): Promise<any> {
    try {
      console.log('渲染场景NFT:', nft.metadata.name);
      
      const mockObject = {
        type: 'Scene',
        nftId: nft.tokenId,
        sceneData: nft.metadata.dl_engine_data?.scene_data,
        position: options?.position || { x: 0, y: 0, z: 0 },
        visible: options?.visible !== false
      };
      
      return mockObject;
    } catch (error) {
      console.error('渲染场景失败:', error);
      throw error;
    }
  }

  /**
   * 默认渲染方式
   */
  private async renderDefault(nft: NFTToken, options?: NFTRenderOptions): Promise<any> {
    try {
      console.log('使用默认方式渲染NFT:', nft.metadata.name);
      
      const mockObject = {
        type: 'Default',
        nftId: nft.tokenId,
        imageUrl: nft.metadata.image,
        position: options?.position || { x: 0, y: 0, z: 0 },
        visible: options?.visible !== false
      };
      
      return mockObject;
    } catch (error) {
      console.error('默认渲染失败:', error);
      throw error;
    }
  }

  /**
   * 获取已渲染的对象
   */
  getRenderedObjects(): Map<string, any> {
    return new Map(this.renderedObjects);
  }

  /**
   * 销毁渲染器
   */
  async destroy(): Promise<void> {
    try {
      // 清理所有渲染对象
      for (const renderObject of this.renderedObjects.values()) {
        await this.removeNFT(renderObject);
      }
      this.renderedObjects.clear();
      
      this.removeAllListeners();
      this.isInitialized = false;
      
      console.log('NFT渲染器已销毁');
    } catch (error) {
      console.error('销毁NFT渲染器失败:', error);
    }
  }
}
