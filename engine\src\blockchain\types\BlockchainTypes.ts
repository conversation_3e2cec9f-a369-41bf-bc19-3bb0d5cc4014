/**
 * 区块链相关类型定义
 */

export interface BlockchainNetwork {
  chainId: number;
  name: string;
  rpcUrl: string;
  blockExplorer: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  testnet?: boolean;
}

export interface Transaction {
  hash: string;
  from: string;
  to: string;
  value: string;
  gasPrice: string;
  gasLimit: string;
  gasUsed?: string;
  blockNumber?: number;
  blockHash?: string;
  timestamp?: number;
  status?: 'pending' | 'confirmed' | 'failed';
}

export interface GasEstimate {
  gasPrice: string;
  gasLimit: string;
  totalCost: string;
  estimatedTime: number; // 预估确认时间（秒）
}

export interface BlockchainConfig {
  defaultNetwork: string;
  networks: Record<string, BlockchainNetwork>;
  walletConnectProjectId?: string;
  infuraProjectId?: string;
  alchemyApiKey?: string;
  enableTestnets: boolean;
  gasOptimization: {
    enabled: boolean;
    maxGasPrice: string;
    priorityFee: string;
  };
}

export interface ContractInfo {
  address: string;
  abi: any[];
  chainId: number;
  deploymentBlock?: number;
}

export interface TokenInfo {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  totalSupply?: string;
  logoURI?: string;
}

export interface BlockchainEvent {
  type: string;
  data: any;
  timestamp: number;
  blockNumber?: number;
  transactionHash?: string;
}

export interface Web3Provider {
  request(args: { method: string; params?: any[] }): Promise<any>;
  on(event: string, handler: (...args: any[]) => void): void;
  removeListener(event: string, handler: (...args: any[]) => void): void;
  isConnected(): boolean;
}

export interface BlockchainState {
  isConnected: boolean;
  currentNetwork: BlockchainNetwork | null;
  currentAccount: string | null;
  balance: string;
  transactions: Transaction[];
  contracts: Record<string, ContractInfo>;
  tokens: TokenInfo[];
}

export interface SmartContractCall {
  contractAddress: string;
  methodName: string;
  parameters: any[];
  value?: string;
  gasLimit?: string;
  gasPrice?: string;
}

export interface SmartContractEvent {
  contractAddress: string;
  eventName: string;
  blockNumber: number;
  transactionHash: string;
  logIndex: number;
  returnValues: Record<string, any>;
}

export interface BlockchainError {
  code: number;
  message: string;
  data?: any;
  stack?: string;
}

// 区块链操作结果
export interface BlockchainResult<T = any> {
  success: boolean;
  data?: T;
  error?: BlockchainError;
  transactionHash?: string;
}

// 合约ABI定义
export interface ContractABI {
  name: string;
  abi: any[];
}

// 合约部署结果
export interface ContractDeployment {
  contractAddress: string;
  transactionHash: string;
  blockNumber: number;
  gasUsed: string;
}

// 批量操作
export interface BatchOperation {
  id: string;
  operations: SmartContractCall[];
  status: 'pending' | 'executing' | 'completed' | 'failed';
  results: BlockchainResult[];
  totalGasCost?: string;
}

// 区块链监听器
export interface BlockchainListener {
  id: string;
  event: string;
  filter?: any;
  callback: (event: BlockchainEvent) => void;
  active: boolean;
}

// 网络状态
export interface NetworkStatus {
  isOnline: boolean;
  latency: number;
  blockHeight: number;
  gasPrice: string;
  congestionLevel: 'low' | 'medium' | 'high';
}

// 钱包状态
export interface WalletStatus {
  isConnected: boolean;
  address: string | null;
  balance: string;
  chainId: number | null;
  walletType: string | null;
}

export enum BlockchainEventType {
  WALLET_CONNECTED = 'wallet_connected',
  WALLET_DISCONNECTED = 'wallet_disconnected',
  NETWORK_CHANGED = 'network_changed',
  ACCOUNT_CHANGED = 'account_changed',
  TRANSACTION_SENT = 'transaction_sent',
  TRANSACTION_CONFIRMED = 'transaction_confirmed',
  TRANSACTION_FAILED = 'transaction_failed',
  CONTRACT_EVENT = 'contract_event',
  BLOCK_MINED = 'block_mined',
  ERROR_OCCURRED = 'error_occurred'
}

export enum TransactionType {
  TRANSFER = 'transfer',
  CONTRACT_CALL = 'contract_call',
  CONTRACT_DEPLOYMENT = 'contract_deployment',
  NFT_MINT = 'nft_mint',
  NFT_TRANSFER = 'nft_transfer',
  TOKEN_APPROVAL = 'token_approval',
  MARKETPLACE_LISTING = 'marketplace_listing',
  MARKETPLACE_PURCHASE = 'marketplace_purchase'
}

export enum NetworkType {
  MAINNET = 'mainnet',
  TESTNET = 'testnet',
  LOCAL = 'local'
}

export enum WalletType {
  METAMASK = 'metamask',
  WALLET_CONNECT = 'walletconnect',
  COINBASE_WALLET = 'coinbase',
  TRUST_WALLET = 'trust',
  INJECTED = 'injected'
}
