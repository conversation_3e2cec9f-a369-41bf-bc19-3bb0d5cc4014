/**
 * Coinbase Wallet适配器 - 处理Coinbase Wallet连接
 */

import { WalletAdapter } from './WalletAdapter';
import { WalletType, BlockchainNetwork, Transaction } from '../types/BlockchainTypes';

export class CoinbaseWalletAdapter extends WalletAdapter {
  private provider: any = null;
  private coinbaseWallet: any = null;

  constructor() {
    super();
    this.type = WalletType.COINBASE_WALLET;
  }

  /**
   * 检查Coinbase Wallet是否可用
   */
  async isAvailable(): Promise<boolean> {
    try {
      // 检查是否安装了Coinbase Wallet扩展
      if (typeof window !== 'undefined') {
        return !!(window as any).ethereum?.isCoinbaseWallet || 
               !!(window as any).coinbaseWalletExtension;
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查是否已连接
   */
  async isConnected(): Promise<boolean> {
    try {
      if (!this.provider) {
        await this.initializeProvider();
      }
      
      if (this.provider) {
        const accounts = await this.provider.request({ method: 'eth_accounts' });
        return accounts && accounts.length > 0;
      }
      
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 连接Coinbase Wallet
   */
  async connect(): Promise<string> {
    try {
      console.log('连接Coinbase Wallet...');
      
      if (!await this.isAvailable()) {
        throw new Error('Coinbase Wallet不可用，请安装Coinbase Wallet扩展');
      }

      await this.initializeProvider();
      
      if (!this.provider) {
        throw new Error('Coinbase Wallet提供者初始化失败');
      }

      // 请求连接账户
      const accounts = await this.provider.request({
        method: 'eth_requestAccounts'
      });

      if (!accounts || accounts.length === 0) {
        throw new Error('未获取到账户信息');
      }

      const address = accounts[0];
      this.isConnectedFlag = true;
      
      // 设置事件监听
      this.setupEventListeners();
      
      console.log('Coinbase Wallet连接成功:', address);
      return address;
      
    } catch (error) {
      console.error('Coinbase Wallet连接失败:', error);
      throw error;
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    try {
      // Coinbase Wallet通常不支持程序化断开连接
      // 用户需要在钱包中手动断开
      
      this.provider = null;
      this.coinbaseWallet = null;
      this.isConnectedFlag = false;
      
      console.log('Coinbase Wallet已断开连接');
    } catch (error) {
      console.error('断开Coinbase Wallet连接失败:', error);
    }
  }

  /**
   * 获取当前链ID
   */
  async getChainId(): Promise<number> {
    try {
      if (!this.provider) {
        throw new Error('Coinbase Wallet未连接');
      }
      
      const chainId = await this.provider.request({ method: 'eth_chainId' });
      return parseInt(chainId, 16);
    } catch (error) {
      console.error('获取链ID失败:', error);
      return 1;
    }
  }

  /**
   * 切换网络
   */
  async switchNetwork(network: BlockchainNetwork): Promise<void> {
    try {
      if (!this.provider) {
        throw new Error('Coinbase Wallet未连接');
      }

      const chainIdHex = '0x' + network.chainId.toString(16);
      
      try {
        // 尝试切换到指定网络
        await this.provider.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: chainIdHex }]
        });
      } catch (switchError: any) {
        // 如果网络不存在，尝试添加网络
        if (switchError.code === 4902) {
          await this.provider.request({
            method: 'wallet_addEthereumChain',
            params: [{
              chainId: chainIdHex,
              chainName: network.name,
              nativeCurrency: network.nativeCurrency,
              rpcUrls: network.rpcUrls,
              blockExplorerUrls: network.blockExplorerUrls
            }]
          });
        } else {
          throw switchError;
        }
      }
      
    } catch (error) {
      console.error('切换网络失败:', error);
      throw error;
    }
  }

  /**
   * 发送交易
   */
  async sendTransaction(transaction: Partial<Transaction>): Promise<string> {
    try {
      if (!this.provider) {
        throw new Error('Coinbase Wallet未连接');
      }

      console.log('发送Coinbase Wallet交易:', transaction);
      
      // 构建交易参数
      const txParams = {
        from: transaction.from,
        to: transaction.to,
        value: transaction.value,
        gasPrice: transaction.gasPrice,
        gas: transaction.gasLimit,
        data: transaction.data
      };

      // 发送交易
      const txHash = await this.provider.request({
        method: 'eth_sendTransaction',
        params: [txParams]
      });
      
      console.log('Coinbase Wallet交易发送成功:', txHash);
      return txHash;
      
    } catch (error) {
      console.error('发送Coinbase Wallet交易失败:', error);
      throw error;
    }
  }

  /**
   * 签名消息
   */
  async signMessage(message: string): Promise<string> {
    try {
      if (!this.provider) {
        throw new Error('Coinbase Wallet未连接');
      }

      console.log('Coinbase Wallet签名消息:', message);
      
      const accounts = await this.provider.request({ method: 'eth_accounts' });
      if (!accounts || accounts.length === 0) {
        throw new Error('未获取到账户信息');
      }

      const signature = await this.provider.request({
        method: 'personal_sign',
        params: [message, accounts[0]]
      });
      
      console.log('Coinbase Wallet消息签名成功');
      return signature;
      
    } catch (error) {
      console.error('Coinbase Wallet消息签名失败:', error);
      throw error;
    }
  }

  /**
   * 获取余额
   */
  async getBalance(address?: string): Promise<string> {
    try {
      if (!this.provider) {
        throw new Error('Coinbase Wallet未连接');
      }

      let targetAddress = address;
      if (!targetAddress) {
        const accounts = await this.provider.request({ method: 'eth_accounts' });
        if (!accounts || accounts.length === 0) {
          throw new Error('未获取到账户信息');
        }
        targetAddress = accounts[0];
      }

      const balance = await this.provider.request({
        method: 'eth_getBalance',
        params: [targetAddress, 'latest']
      });
      
      return balance;
      
    } catch (error) {
      console.error('获取余额失败:', error);
      return '0';
    }
  }

  /**
   * 初始化提供者
   */
  private async initializeProvider(): Promise<void> {
    try {
      if (typeof window === 'undefined') {
        throw new Error('不支持的环境');
      }

      const windowEth = (window as any).ethereum;
      
      // 检查是否是Coinbase Wallet
      if (windowEth?.isCoinbaseWallet) {
        this.provider = windowEth;
      } else if ((window as any).coinbaseWalletExtension) {
        this.provider = (window as any).coinbaseWalletExtension;
      } else {
        // 如果没有检测到Coinbase Wallet，尝试使用通用的ethereum对象
        // 但需要确认是Coinbase Wallet
        const providers = windowEth?.providers;
        if (providers) {
          this.provider = providers.find((p: any) => p.isCoinbaseWallet);
        }
        
        if (!this.provider) {
          throw new Error('未检测到Coinbase Wallet');
        }
      }
      
    } catch (error) {
      console.error('初始化Coinbase Wallet提供者失败:', error);
      throw error;
    }
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    if (!this.provider) return;

    // 监听账户变化
    this.provider.on('accountsChanged', (accounts: string[]) => {
      console.log('Coinbase Wallet账户变化:', accounts);
      this.emit('accountsChanged', accounts);
    });

    // 监听网络变化
    this.provider.on('chainChanged', (chainId: string) => {
      console.log('Coinbase Wallet网络变化:', chainId);
      this.emit('chainChanged', parseInt(chainId, 16));
    });

    // 监听连接状态变化
    this.provider.on('connect', (connectInfo: any) => {
      console.log('Coinbase Wallet连接:', connectInfo);
      this.emit('connect', connectInfo);
    });

    // 监听断开连接
    this.provider.on('disconnect', (error: any) => {
      console.log('Coinbase Wallet断开连接:', error);
      this.emit('disconnect', error);
    });
  }
}
