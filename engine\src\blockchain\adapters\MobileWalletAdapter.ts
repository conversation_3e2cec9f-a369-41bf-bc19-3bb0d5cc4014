/**
 * 移动端钱包适配器
 */

import { WalletAdapter } from '../wallet/WalletAdapter';
import { EventEmitter } from '../../utils/EventEmitter';
import { WalletType, BlockchainNetwork, Transaction } from '../types/BlockchainTypes';

export interface MobileWalletConfig {
  name: string;
  walletType: WalletType;
  deepLinkScheme: string;
  universalLink: string;
  appStoreUrl: string;
  playStoreUrl: string;
  supportedMethods: string[];
  qrCodeTimeout: number;
}

export interface MobileWalletConnectionOptions {
  method: 'deeplink' | 'qrcode' | 'injected';
  timeout?: number;
  redirectUrl?: string;
}

export interface WalletConnectionResult {
  address: string;
  chainId: number;
  provider: any;
}

export class MobileWalletAdapter extends WalletAdapter {
  private mobileConfig: MobileWalletConfig;
  private connectionMethod: 'deeplink' | 'qrcode' | 'injected' | null = null;
  private qrCodeData: string | null = null;
  private connectionTimeout: NodeJS.Timeout | null = null;
  private connected: boolean = false;
  private connectionResult: WalletConnectionResult | null = null;

  constructor(config: MobileWalletConfig) {
    super(config.walletType);
    this.mobileConfig = config;
  }

  /**
   * 检查钱包是否可用
   */
  async isAvailable(): Promise<boolean> {
    return await this.isWalletInstalled();
  }

  /**
   * 检查钱包是否已连接
   */
  async isConnected(): Promise<boolean> {
    return this.connected;
  }

  /**
   * 获取当前账户地址
   */
  async getAddress(): Promise<string> {
    if (!this.connectionResult) {
      throw new Error('Wallet not connected');
    }
    return this.connectionResult.address;
  }

  /**
   * 获取账户余额
   */
  async getBalance(address?: string): Promise<string> {
    const targetAddress = address || await this.getAddress();
    // 这里应该实现获取余额的逻辑
    // 简化实现，返回模拟值
    return '0';
  }

  /**
   * 获取当前链ID
   */
  async getChainId(): Promise<number> {
    if (!this.connectionResult) {
      throw new Error('Wallet not connected');
    }
    return this.connectionResult.chainId;
  }

  /**
   * 切换网络
   */
  async switchNetwork(network: BlockchainNetwork): Promise<void> {
    // 移动端网络切换实现
    throw new Error('Network switching not implemented for mobile wallets');
  }

  /**
   * 签名类型化数据
   */
  async signTypedData(domain: any, types: any, value: any): Promise<string> {
    // 移动端类型化数据签名实现
    throw new Error('Typed data signing not implemented for mobile wallets');
  }

  /**
   * 添加代币到钱包
   */
  async addToken(tokenAddress: string, tokenSymbol: string, tokenDecimals: number, tokenImage?: string): Promise<boolean> {
    // 移动端添加代币实现
    return false;
  }

  /**
   * 监听账户变化
   */
  protected setupAccountListener(): void {
    // 移动端账户监听实现
  }

  /**
   * 监听网络变化
   */
  protected setupNetworkListener(): void {
    // 移动端网络监听实现
  }

  /**
   * 监听连接状态变化
   */
  protected setupConnectionListener(): void {
    // 移动端连接状态监听实现
  }

  /**
   * 设置连接状态
   */
  private setConnected(connected: boolean, result?: WalletConnectionResult): void {
    this.connected = connected;
    this.connectionResult = result || null;
    this.emit(connected ? 'connected' : 'disconnected', result);
  }

  /**
   * 检测移动设备类型
   */
  private detectMobileDevice(): 'ios' | 'android' | 'desktop' {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (/iphone|ipad|ipod/.test(userAgent)) {
      return 'ios';
    } else if (/android/.test(userAgent)) {
      return 'android';
    } else {
      return 'desktop';
    }
  }

  /**
   * 检查钱包应用是否已安装
   */
  async isWalletInstalled(): Promise<boolean> {
    const device = this.detectMobileDevice();
    
    if (device === 'desktop') {
      // 桌面端检查注入的钱包
      return !!(window as any)[this.mobileConfig.name.toLowerCase()];
    }

    // 移动端通过尝试打开深链接来检测
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        resolve(false);
      }, 2000);

      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.src = `${this.mobileConfig.deepLinkScheme}://`;
      
      document.body.appendChild(iframe);
      
      // 如果应用已安装，页面会被切换，否则会触发错误
      iframe.onload = () => {
        clearTimeout(timeout);
        document.body.removeChild(iframe);
        resolve(true);
      };
      
      iframe.onerror = () => {
        clearTimeout(timeout);
        document.body.removeChild(iframe);
        resolve(false);
      };
    });
  }

  /**
   * 连接钱包 - 重写基类方法以符合接口
   */
  async connect(): Promise<string> {
    const result = await this.connectWithOptions({ method: 'qrcode' });
    return result.address;
  }

  /**
   * 使用选项连接钱包
   */
  async connectWithOptions(options: MobileWalletConnectionOptions = { method: 'qrcode' }): Promise<WalletConnectionResult> {
    try {
      this.connectionMethod = options.method;

      const device = this.detectMobileDevice();
      const isInstalled = await this.isWalletInstalled();

      let result: WalletConnectionResult;

      switch (options.method) {
        case 'injected':
          result = await this.connectInjected();
          break;

        case 'deeplink':
          if (device === 'desktop') {
            throw new Error('Deep link connection is not supported on desktop');
          }
          result = await this.connectDeepLink(isInstalled, options);
          break;

        case 'qrcode':
          result = await this.connectQRCode(options);
          break;

        default:
          throw new Error(`Unsupported connection method: ${options.method}`);
      }

      this.setConnected(true, result);
      return result;
    } catch (error) {
      this.setConnected(false);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 通过注入的钱包连接
   */
  private async connectInjected(): Promise<WalletConnectionResult> {
    const walletObject = (window as any)[this.mobileConfig.name.toLowerCase()];

    if (!walletObject) {
      throw new Error(`${this.mobileConfig.name} wallet not found`);
    }

    const accounts = await walletObject.request({
      method: 'eth_requestAccounts',
    });

    if (!accounts || accounts.length === 0) {
      throw new Error('No accounts found');
    }

    const chainId = await walletObject.request({
      method: 'eth_chainId',
    });

    const result: WalletConnectionResult = {
      address: accounts[0],
      chainId: parseInt(chainId, 16),
      provider: walletObject,
    };

    this.setConnected(true, result);
    return result;
  }

  /**
   * 通过深链接连接
   */
  private async connectDeepLink(
    isInstalled: boolean, 
    options: MobileWalletConnectionOptions
  ): Promise<WalletConnectionResult> {
    if (!isInstalled) {
      // 钱包未安装，引导用户安装
      this.redirectToAppStore();
      throw new Error('Wallet app is not installed');
    }

    // 生成连接请求
    const connectionRequest = this.generateConnectionRequest();
    const deepLinkUrl = `${this.mobileConfig.deepLinkScheme}://wc?uri=${encodeURIComponent(connectionRequest)}`;

    // 打开深链接
    window.location.href = deepLinkUrl;

    // 等待连接结果
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, options.timeout || 30000);

      // 监听页面可见性变化（用户从钱包应用返回）
      const handleVisibilityChange = () => {
        if (!document.hidden) {
          // 页面重新可见，检查连接状态
          this.checkConnectionStatus()
            .then((result) => {
              if (result) {
                clearTimeout(timeout);
                document.removeEventListener('visibilitychange', handleVisibilityChange);
                resolve(result);
              }
            })
            .catch(reject);
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);
    });
  }

  /**
   * 通过二维码连接
   */
  private async connectQRCode(options: MobileWalletConnectionOptions): Promise<WalletConnectionResult> {
    // 生成WalletConnect URI
    const connectionRequest = this.generateConnectionRequest();
    this.qrCodeData = connectionRequest;

    this.emit('qrCodeGenerated', {
      uri: connectionRequest,
      qrCodeData: this.qrCodeData,
    });

    // 等待连接
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.qrCodeData = null;
        reject(new Error('QR code connection timeout'));
      }, options.timeout || this.mobileConfig.qrCodeTimeout || 300000); // 5分钟默认超时

      // 监听连接事件
      const handleConnection = (result: WalletConnectionResult) => {
        clearTimeout(timeout);
        this.qrCodeData = null;
        resolve(result);
      };

      this.once('connected', handleConnection);
    });
  }

  /**
   * 生成连接请求
   */
  private generateConnectionRequest(): string {
    // 这里应该生成WalletConnect URI或其他连接协议的请求
    const sessionId = this.generateSessionId();
    const bridgeUrl = 'https://bridge.walletconnect.org';
    const key = this.generateKey();
    
    return `wc:${sessionId}@1?bridge=${encodeURIComponent(bridgeUrl)}&key=${key}`;
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return Array.from(crypto.getRandomValues(new Uint8Array(16)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * 生成密钥
   */
  private generateKey(): string {
    return Array.from(crypto.getRandomValues(new Uint8Array(32)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * 检查连接状态
   */
  private async checkConnectionStatus(): Promise<WalletConnectionResult | null> {
    // 这里应该检查WalletConnect会话状态
    // 简化实现，实际应该查询WalletConnect桥接服务器
    return null;
  }

  /**
   * 重定向到应用商店
   */
  private redirectToAppStore(): void {
    const device = this.detectMobileDevice();
    let storeUrl: string;

    switch (device) {
      case 'ios':
        storeUrl = this.mobileConfig.appStoreUrl;
        break;
      case 'android':
        storeUrl = this.mobileConfig.playStoreUrl;
        break;
      default:
        // 桌面端显示下载页面
        storeUrl = this.mobileConfig.universalLink;
    }

    window.open(storeUrl, '_blank');
  }

  /**
   * 获取二维码数据
   */
  getQRCodeData(): string | null {
    return this.qrCodeData;
  }

  /**
   * 获取连接方法
   */
  getConnectionMethod(): string | null {
    return this.connectionMethod;
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    try {
      if (this.connectionTimeout) {
        clearTimeout(this.connectionTimeout);
        this.connectionTimeout = null;
      }

      this.qrCodeData = null;
      this.connectionMethod = null;
      this.setConnected(false);

      this.emit('disconnected');
    } catch (error) {
      console.error('Failed to disconnect mobile wallet:', error);
      throw error;
    }
  }

  /**
   * 发送交易
   */
  async sendTransaction(transaction: Partial<Transaction>): Promise<string> {
    if (!this.connected || !this.connectionResult) {
      throw new Error('Wallet not connected');
    }

    const device = this.detectMobileDevice();

    if (device !== 'desktop' && this.connectionMethod !== 'injected') {
      // 移动端通过深链接发送交易
      return this.sendTransactionViaDeepLink(transaction);
    } else {
      // 桌面端或注入钱包直接发送
      return this.sendTransactionViaInjected(transaction);
    }
  }

  /**
   * 通过注入钱包发送交易
   */
  private async sendTransactionViaInjected(transaction: Partial<Transaction>): Promise<string> {
    const walletObject = (window as any)[this.mobileConfig.name.toLowerCase()];

    if (!walletObject) {
      throw new Error(`${this.mobileConfig.name} wallet not found`);
    }

    const params = this.formatTransactionParams(transaction);
    const txHash = await walletObject.request({
      method: 'eth_sendTransaction',
      params: [params],
    });

    return txHash;
  }

  /**
   * 通过深链接发送交易
   */
  private async sendTransactionViaDeepLink(transaction: any): Promise<string> {
    const transactionData = JSON.stringify(transaction);
    const deepLinkUrl = `${this.mobileConfig.deepLinkScheme}://transaction?data=${encodeURIComponent(transactionData)}`;

    // 打开深链接
    window.location.href = deepLinkUrl;

    // 等待交易结果
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Transaction timeout'));
      }, 60000); // 1分钟超时

      // 监听页面可见性变化
      const handleVisibilityChange = () => {
        if (!document.hidden) {
          // 检查交易状态
          this.checkTransactionStatus()
            .then((txHash) => {
              if (txHash) {
                clearTimeout(timeout);
                document.removeEventListener('visibilitychange', handleVisibilityChange);
                resolve(txHash);
              }
            })
            .catch(reject);
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);
    });
  }

  /**
   * 检查交易状态
   */
  private async checkTransactionStatus(): Promise<string | null> {
    // 这里应该检查交易状态
    // 简化实现，实际应该查询区块链或钱包服务
    return null;
  }

  /**
   * 签名消息
   */
  async signMessage(message: string): Promise<string> {
    if (!this.connected || !this.connectionResult) {
      throw new Error('Wallet not connected');
    }

    const device = this.detectMobileDevice();

    if (device !== 'desktop' && this.connectionMethod !== 'injected') {
      // 移动端通过深链接签名
      return this.signMessageViaDeepLink(message);
    } else {
      // 桌面端或注入钱包直接签名
      return this.signMessageViaInjected(message);
    }
  }

  /**
   * 通过注入钱包签名消息
   */
  private async signMessageViaInjected(message: string): Promise<string> {
    const walletObject = (window as any)[this.mobileConfig.name.toLowerCase()];

    if (!walletObject) {
      throw new Error(`${this.mobileConfig.name} wallet not found`);
    }

    const signature = await walletObject.request({
      method: 'personal_sign',
      params: [message, this.connectionResult!.address],
    });

    return signature;
  }

  /**
   * 通过深链接签名消息
   */
  private async signMessageViaDeepLink(message: string): Promise<string> {
    const deepLinkUrl = `${this.mobileConfig.deepLinkScheme}://sign?message=${encodeURIComponent(message)}`;

    // 打开深链接
    window.location.href = deepLinkUrl;

    // 等待签名结果
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Signing timeout'));
      }, 60000);

      // 监听页面可见性变化
      const handleVisibilityChange = () => {
        if (!document.hidden) {
          // 检查签名结果
          this.checkSignatureStatus()
            .then((signature) => {
              if (signature) {
                clearTimeout(timeout);
                document.removeEventListener('visibilitychange', handleVisibilityChange);
                resolve(signature);
              }
            })
            .catch(reject);
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);
    });
  }

  /**
   * 检查签名状态
   */
  private async checkSignatureStatus(): Promise<string | null> {
    // 这里应该检查签名状态
    // 简化实现，实际应该查询钱包服务
    return null;
  }

  /**
   * 获取支持的连接方法
   */
  getSupportedConnectionMethods(): string[] {
    const device = this.detectMobileDevice();
    const methods: string[] = [];

    if (device === 'desktop') {
      methods.push('injected', 'qrcode');
    } else {
      methods.push('deeplink', 'qrcode');
      
      // 检查是否有注入的钱包（如移动浏览器内的钱包）
      if ((window as any)[this.mobileConfig.name.toLowerCase()]) {
        methods.push('injected');
      }
    }

    return methods;
  }

  /**
   * 获取推荐的连接方法
   */
  getRecommendedConnectionMethod(): string {
    const device = this.detectMobileDevice();
    
    if (device === 'desktop') {
      return 'injected';
    } else {
      return 'deeplink';
    }
  }
}
