/**
 * NFT类型定义测试
 */

import { 
  NFTToken, 
  NFTMetadata, 
  MintingStatus, 
  MintingStatusEnum,
  AssetType,
  LicenseType,
  NFTRenderOptions 
} from '../types/NFTTypes';

export class NFTTypesTest {
  
  /**
   * 测试NFT令牌类型
   */
  testNFTTokenType(): void {
    console.log('测试NFT令牌类型...');
    
    const nftToken: NFTToken = {
      tokenId: '123',
      contractAddress: '0x1234567890123456789012345678901234567890',
      chainId: 1,
      owner: '0xowner123456789012345678901234567890',
      creator: '0xcreator123456789012345678901234567890',
      metadata: this.createTestMetadata(),
      tokenURI: 'ipfs://QmTestHash123456789',
      mintedAt: new Date(),
      transferCount: 0,
      isForSale: false,
      royaltyRecipient: '0xcreator123456789012345678901234567890',
      royaltyPercentage: 500 // 5%
    };
    
    console.log('✅ NFT令牌类型测试通过');
    console.log('NFT令牌:', nftToken);
  }

  /**
   * 测试铸造状态类型
   */
  testMintingStatusType(): void {
    console.log('测试铸造状态类型...');
    
    const mintingStatus: MintingStatus = {
      assetId: 'test-asset-1',
      status: MintingStatusEnum.PREPARING,
      progress: 0,
      startTime: new Date(),
      metadata: this.createTestMetadata()
    };
    
    // 测试状态更新
    mintingStatus.status = MintingStatusEnum.UPLOADING_METADATA;
    mintingStatus.progress = 20;
    mintingStatus.lastUpdate = new Date();
    
    mintingStatus.status = MintingStatusEnum.UPLOADING_ASSETS;
    mintingStatus.progress = 40;
    
    mintingStatus.status = MintingStatusEnum.MINTING;
    mintingStatus.progress = 60;
    
    mintingStatus.status = MintingStatusEnum.CONFIRMING;
    mintingStatus.progress = 80;
    
    mintingStatus.status = MintingStatusEnum.COMPLETED;
    mintingStatus.progress = 100;
    mintingStatus.transactionHash = '0xtxhash123456789';
    
    console.log('✅ 铸造状态类型测试通过');
    console.log('铸造状态:', mintingStatus);
  }

  /**
   * 测试NFT渲染选项类型
   */
  testNFTRenderOptionsType(): void {
    console.log('测试NFT渲染选项类型...');
    
    const renderOptions: NFTRenderOptions = {
      quality: 'high',
      enableAnimations: true,
      enableInteractions: true,
      enablePhysics: false,
      enableAudio: true,
      maxTextureSize: 2048,
      lodLevel: 2,
      renderDistance: 100,
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 },
      visible: true,
      autoPlay: true,
      loop: false,
      volume: 0.8
    };
    
    console.log('✅ NFT渲染选项类型测试通过');
    console.log('渲染选项:', renderOptions);
  }

  /**
   * 测试资产类型枚举
   */
  testAssetTypeEnum(): void {
    console.log('测试资产类型枚举...');
    
    const assetTypes = [
      AssetType.MODEL_3D,
      AssetType.MODEL,
      AssetType.TEXTURE,
      AssetType.AUDIO,
      AssetType.SCENE,
      AssetType.ANIMATION,
      AssetType.MATERIAL,
      AssetType.SCRIPT,
      AssetType.ENVIRONMENT,
      AssetType.CHARACTER,
      AssetType.PROP
    ];
    
    console.log('✅ 资产类型枚举测试通过');
    console.log('支持的资产类型:', assetTypes);
  }

  /**
   * 测试铸造状态枚举
   */
  testMintingStatusEnum(): void {
    console.log('测试铸造状态枚举...');
    
    const statuses = [
      MintingStatusEnum.PREPARING,
      MintingStatusEnum.UPLOADING_METADATA,
      MintingStatusEnum.UPLOADING_ASSETS,
      MintingStatusEnum.MINTING,
      MintingStatusEnum.CONFIRMING,
      MintingStatusEnum.COMPLETED,
      MintingStatusEnum.FAILED,
      MintingStatusEnum.CANCELLED
    ];
    
    console.log('✅ 铸造状态枚举测试通过');
    console.log('支持的铸造状态:', statuses);
  }

  /**
   * 创建测试元数据
   */
  private createTestMetadata(): NFTMetadata {
    return {
      name: '测试NFT',
      description: '这是一个用于测试的NFT',
      image: 'https://example.com/test-image.png',
      attributes: [
        {
          trait_type: '稀有度',
          value: '普通'
        },
        {
          trait_type: '等级',
          value: 1,
          display_type: 'number'
        }
      ],
      dl_engine_data: {
        asset_type: AssetType.MODEL_3D,
        engine_version: '1.0.0',
        creation_timestamp: Date.now(),
        creator_address: '0xcreator123456789012345678901234567890',
        license_type: LicenseType.CC0,
        technical_metadata: {
          file_format: 'glb',
          file_size: 1024000,
          compression: 'gzip',
          quality_level: 8,
          polygon_count: 5000,
          texture_count: 3,
          animation_count: 2
        },
        interaction_metadata: {
          interaction_types: [],
          input_methods: [],
          supported_devices: [],
          vr_compatible: true,
          ar_compatible: false,
          multiplayer_support: false
        }
      }
    };
  }

  /**
   * 运行所有测试
   */
  runAllTests(): void {
    console.log('开始NFT类型定义测试...');
    
    try {
      this.testNFTTokenType();
      this.testMintingStatusType();
      this.testNFTRenderOptionsType();
      this.testAssetTypeEnum();
      this.testMintingStatusEnum();
      
      console.log('✅ 所有NFT类型定义测试通过！');
    } catch (error) {
      console.error('❌ NFT类型定义测试失败:', error);
      throw error;
    }
  }
}

// 导出测试函数
export function runNFTTypesTests(): void {
  const test = new NFTTypesTest();
  test.runAllTests();
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runNFTTypesTests();
}
